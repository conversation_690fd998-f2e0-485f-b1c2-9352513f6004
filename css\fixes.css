/* ===== CSS Fixes for Professional Design ===== */

/* Fix spacing and layout issues */
body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Header fixes */
.header {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
    position: relative;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    position: relative;
}

/* Ensure navbar content doesn't overlap with language switcher */
.nav-menu {
    margin-right: 120px;
}

/* Language switcher positioning - Fixed Above Logo Issue */
.language-switcher {
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%);
    z-index: 1001;
}

.lang-btn {
    background: #ffffff !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    text-decoration: none !important;
    min-width: 100px !important;
    justify-content: center !important;
}

.lang-btn:hover {
    background: #f8fafc !important;
    border-color: #2563eb !important;
    color: #2563eb !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(37, 99, 235, 0.15) !important;
}

.lang-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.lang-btn .flag-icon {
    width: 20px !important;
    height: 20px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

.lang-btn .lang-text {
    font-weight: 600 !important;
    color: inherit !important;
}

/* Brand styling */
.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
}

.logo {
    height: 45px;
    width: auto;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    font-family: 'Cairo', sans-serif;
}

/* Navigation menu */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-link {
    color: #1f2937 !important;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.75rem 0;
    font-size: 0.95rem;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Dropdown menu improvements */
.dropdown-menu {
    background: #ffffff !important;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    border-radius: 0.75rem;
}

.dropdown-menu a {
    color: #374151 !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
}

.dropdown-menu a:hover {
    background: #f8fafc !important;
    color: #2563eb !important;
}

/* Action buttons */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Button improvements */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: #2563eb;
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.35);
    color: #ffffff;
}

.btn-outline {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-outline:hover {
    background: #2563eb;
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 0.75rem;
    min-width: 180px;
    justify-content: center;
}

/* Service card button improvements */
.service-card .btn {
    margin-top: 1.5rem;
    width: 100%;
    max-width: 250px;
    position: relative;
    overflow: hidden;
}

.service-card .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.service-card .btn:hover::before {
    left: 100%;
}

/* Button secondary and accent styles for service cards */
.service-card .btn-secondary {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    border: none;
}

.service-card .btn-secondary:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    transform: translateY(-2px);
}

/* Button success style for WhatsApp */
.btn-success {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-success:hover {
    background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

/* Unified button styling for contact section */
.contact .btn-primary,
.contact .btn-secondary,
.contact .btn-success {
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    min-width: 160px;
    font-size: 14px;
}

.contact .btn-secondary {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.contact .btn-secondary:hover {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

/* Enhanced contact card hover effects */
.contact .pricing-card:hover .icon {
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.contact .pricing-card:hover .icon-primary {
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
}

.contact .pricing-card:hover .icon-secondary {
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
}

.contact .pricing-card:hover .icon-success {
    box-shadow: 0 15px 35px rgba(37, 211, 102, 0.4);
}

.contact .pricing-card:hover .icon-accent {
    box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
}

.contact .pricing-card:hover .icon-cyan {
    box-shadow: 0 15px 35px rgba(8, 145, 178, 0.4);
}

/* Additional hover effects for pricing cards with cyan icons */
.pricing-card:hover .icon-cyan {
    transform: scale(1.1);
    box-shadow: 0 15px 35px rgba(8, 145, 178, 0.4);
}

.service-card .btn-accent {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    border: none;
}

.service-card .btn-accent:hover {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    transform: translateY(-2px);
}

/* Hero section fixes - Improved Layout */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: calc(80px + 4rem) 0 4rem;
    margin-top: 0;
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
}

.hero-overlay {
    background: rgba(30, 64, 175, 0.1) !important;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.hero-text {
    position: relative;
    z-index: 3;
}

.hero-visual {
    position: relative;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hero text improvements */
.hero-title {
    color: #ffffff !important;
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-title .gradient-text {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    display: block;
    margin-top: 0.5rem;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.95) !important;
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.hero-badge {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Hero stats improvements */
.hero-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    justify-content: flex-start;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 900;
    color: #fbbf24 !important;
    margin-bottom: 0.25rem;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

/* Hero buttons */
.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.hero-buttons .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 0.75rem;
}

/* Hero visual section - Fixed Layout */
.hero-visual {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Modern Dashboard Styles */
.modern-dashboard {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 0;
    height: 500px;
}

/* Dashboard Main Card */
.dashboard-main {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 2rem;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 25px rgba(0, 0, 0, 0.05);
    z-index: 10;
    animation: slideInRight 1s ease-out;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #10b981;
    font-weight: 600;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

.dashboard-title h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #1f2937;
    font-weight: 700;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.metric-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Metric Icons and Data */
.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    color: white;
    flex-shrink: 0;
}

.metric-item:nth-child(1) .metric-icon {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.metric-item:nth-child(2) .metric-icon {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.metric-item:nth-child(3) .metric-icon {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.metric-data {
    flex: 1;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
}

.metric-unit {
    font-size: 0.9rem;
    color: #6b7280;
    margin-left: 2px;
}

.metric-data p {
    margin: 0.25rem 0 0 0;
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
}

/* Progress Section */
.progress-section {
    margin-top: 1rem;
}

.progress-item {
    margin-bottom: 1rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #4b5563;
    font-weight: 500;
}

.progress-value {
    color: #1f2937;
    font-weight: 600;
}

.progress-bar {
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2563eb, #3b82f6);
    border-radius: 3px;
    width: 0;
    transition: width 2s ease-out;
    animation: progressFill 2s ease-out forwards;
}

.progress-item:nth-child(2) .progress-fill {
    background: linear-gradient(90deg, #10b981, #34d399);
    animation-delay: 0.5s;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.floating-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(20px);
    opacity: 0.5;
    animation: floatOrb 10s infinite ease-in-out;
}

.orb-1 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, rgba(37, 99, 235, 0.1) 70%);
    top: -30px;
    right: -30px;
    animation-delay: 0s;
}

.orb-2 {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, rgba(4, 120, 87, 0.1) 70%);
    bottom: 40px;
    left: 20px;
    animation-delay: 2s;
}

.orb-3 {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.3) 0%, rgba(217, 119, 6, 0.1) 70%);
    top: 50%;
    right: 30px;
    animation-delay: 4s;
}

/* Service Icons */
.service-icon {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    animation: floatIcon 6s infinite ease-in-out;
    z-index: 2;
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.icon-hosting {
    top: 20px;
    left: 50px;
    color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    border: 1px solid rgba(255, 193, 7, 0.3);
    animation-delay: 0s;
}

.icon-security {
    top: 60%;
    right: 80px;
    color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    border: 1px solid rgba(245, 158, 11, 0.2);
    animation-delay: 1.5s;
}

.icon-speed {
    bottom: 80px;
    left: 30px;
    color: #f59e0b;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    border: 1px solid rgba(245, 158, 11, 0.2);
    animation-delay: 3s;
}

.icon-support {
    top: 30%;
    left: 20px;
    color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.1));
    border: 1px solid rgba(255, 193, 7, 0.3);
    animation-delay: 4.5s;
}

.icon-support.elevated {
    transform: translateY(-25px);
}

/* Stats Cards */
.stats-cards {
    position: absolute;
    bottom: -20px;
    right: -20px;
    display: flex;
    gap: 1rem;
    z-index: 5;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(37, 99, 235, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    animation: slideInUp 1s ease-out;
    min-width: 120px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.clients-card {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.stat-card.support-card {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    animation-delay: 0.2s;
}

.stat-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #f59e0b;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.stat-content {
    text-align: left;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #f59e0b;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.7rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.2;
}

/* Specific card styling with enhanced gradients */
.floating-card:nth-child(1) {
    border-left: 3px solid #2563eb;
}

.floating-card:nth-child(1) .icon {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
}

.floating-card:nth-child(2) {
    border-left: 3px solid #10b981;
}

.floating-card:nth-child(2) .icon {
    background: linear-gradient(135deg, #047857 0%, #10b981 50%, #34d399 100%);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.floating-card:nth-child(3) {
    border-left: 3px solid #f59e0b;
}

.floating-card:nth-child(3) .icon {
    background: linear-gradient(135deg, #d97706 0%, #f59e0b 50%, #fbbf24 100%);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

.floating-card:nth-child(4) {
    border-left: 3px solid #8b5cf6;
}

.floating-card:nth-child(4) .icon {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 50%, #a78bfa 100%);
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
}

/* Hover effects for specific cards */
.floating-card:nth-child(1):hover {
    border-left-color: #1e40af;
    box-shadow: 0 20px 40px rgba(37, 99, 235, 0.15), 0 8px 25px rgba(0, 0, 0, 0.08);
}

.floating-card:nth-child(2):hover {
    border-left-color: #047857;
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15), 0 8px 25px rgba(0, 0, 0, 0.08);
}

.floating-card:nth-child(3):hover {
    border-left-color: #d97706;
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.15), 0 8px 25px rgba(0, 0, 0, 0.08);
}

.floating-card:nth-child(4):hover {
    border-left-color: #7c3aed;
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15), 0 8px 25px rgba(0, 0, 0, 0.08);
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes progressFill {
    from {
        width: 0;
    }
    to {
        width: var(--progress-width, 0);
    }
}

@keyframes floatOrb {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

@keyframes floatIcon {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (min-width: 1400px) {
    .modern-dashboard {
        max-width: 700px;
        height: 550px;
    }

    .dashboard-main {
        padding: 2.5rem;
    }

    .metrics-grid {
        gap: 1.5rem;
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .metric-value {
        font-size: 1.8rem;
    }

    .stat-card {
        padding: 1.25rem;
        min-width: 100px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

@media (min-width: 1024px) and (max-width: 1399px) {
    .modern-dashboard {
        max-width: 650px;
        height: 520px;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .modern-dashboard {
        max-width: 550px;
        height: 480px;
    }

    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .metric-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .metric-value {
        font-size: 1.3rem;
    }

    .stat-card {
        min-width: 70px;
    }
}

/* Pulse animation for icons */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.floating-card .icon {
    animation: iconPulse 3s ease-in-out infinite;
}

.floating-card:hover .icon {
    animation: none;
}

/* Section spacing fixes */
.section {
    padding: 5rem 0;
    margin: 2rem 0;
}

.section-header {
    margin-bottom: 4rem;
    padding: 0 1.5rem;
    text-align: center;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #ffffff !important;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    color: #111827 !important;
}

.section-subtitle {
    font-size: 1.25rem;
    color: #4b5563 !important;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    font-weight: 500;
}

/* Services grid fixes - 3 Cards Layout */
.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-top: 4rem;
    padding: 0 1.5rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.service-card {
    text-align: center;
    transition: all 0.4s ease;
    padding: 3.5rem 2.5rem;
    margin-bottom: 1.5rem;
    background: #ffffff;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #3b82f6);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.service-card.card-secondary::before {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.service-card.card-accent::before {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.service-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border-color: #e2e8f0;
}

.service-card:hover::before {
    transform: scaleX(1);
}

/* Service header improvements */
.service-header {
    margin-bottom: 2rem;
}

.service-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

/* Service features styling */
.service-features {
    margin: 2rem 0;
    text-align: left;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    color: #4b5563;
    padding: 0.5rem 0;
}

.feature-item i {
    color: #2563eb;
    font-size: 1.1rem;
    width: 20px;
    flex-shrink: 0;
}

.service-card.card-secondary .feature-item i {
    color: #10b981;
}

.service-card.card-accent .feature-item i {
    color: #f59e0b;
}

/* Service stats */
.service-stats {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
    padding: 1.5rem 0;
    border-top: 1px solid #f1f5f9;
    border-bottom: 1px solid #f1f5f9;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.75rem;
    font-weight: 900;
    color: #2563eb;
    margin-bottom: 0.25rem;
}

.service-card.card-secondary .stat-number {
    color: #10b981;
}

.service-card.card-accent .stat-number {
    color: #f59e0b;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Icon improvements */
.icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 1.25rem;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    position: relative;
    transition: all 0.4s ease;
}

.icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 1.25rem;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.service-card:hover .icon::before {
    opacity: 1;
}

.icon-xl {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
    border-radius: 1.5rem;
}

.icon-primary {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(37, 99, 235, 0.3);
}

.service-card:hover .icon-primary {
    box-shadow: 0 20px 40px 0 rgba(37, 99, 235, 0.4);
    transform: scale(1.05);
}

.icon-secondary {
    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(16, 185, 129, 0.3);
}

.service-card:hover .icon-secondary {
    box-shadow: 0 20px 40px 0 rgba(16, 185, 129, 0.4);
    transform: scale(1.05);
}

.icon-accent {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(245, 158, 11, 0.3);
}

.service-card:hover .icon-accent {
    box-shadow: 0 20px 40px 0 rgba(245, 158, 11, 0.4);
    transform: scale(1.05);
}

.icon-cyan {
    background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
    color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(8, 145, 178, 0.3);
}

.service-card:hover .icon-cyan {
    box-shadow: 0 20px 40px 0 rgba(8, 145, 178, 0.4);
    transform: scale(1.05);
}

.icon-success {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(37, 211, 102, 0.3);
}

.service-card:hover .icon-success,
.pricing-card:hover .icon-success {
    box-shadow: 0 20px 40px 0 rgba(37, 211, 102, 0.4);
    transform: scale(1.05);
}

/* Tablet responsiveness */
@media (max-width: 1024px) {
    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        max-width: 600px;
    }

    .service-card {
        padding: 3rem 2.5rem;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .navbar .container {
        padding: 0 1rem;
    }
    
    .language-switcher {
        top: 0.25rem;
        right: 0.5rem;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.125rem !important;
    }

    .hero-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .stat-item {
        padding: 0.75rem 1rem;
        min-width: 120px;
    }

    .modern-dashboard {
        max-width: 400px;
        height: 420px;
        padding: 1rem 0;
    }

    .dashboard-main {
        padding: 1.5rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metric-item {
        padding: 0.75rem;
    }

    .metric-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .metric-value {
        font-size: 1.2rem;
    }

    .stats-cards {
        position: static;
        margin-top: 1rem;
        justify-content: center;
    }

    .stat-card {
        min-width: 60px;
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.65rem;
    }

    .floating-elements {
        display: none;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .modern-dashboard {
        max-width: 320px;
        height: 380px;
    }

    .dashboard-main {
        padding: 1.25rem;
    }

    .dashboard-title h3 {
        font-size: 1rem;
    }

    .metric-value {
        font-size: 1.1rem;
    }

    .metric-data p {
        font-size: 0.75rem;
    }
}

    .language-switcher {
        top: 0.5rem;
        right: 1rem;
    }

    .lang-btn {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.8rem !important;
        min-width: 80px !important;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        padding: 0 1rem;
    }

    .service-card {
        padding: 2.5rem 2rem;
    }

    .icon-xl {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .service-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat {
        padding: 0.5rem 0;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        gap: 4px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
    }
    
    .mobile-menu-toggle span {
        width: 25px;
        height: 3px;
        background: #374151;
        border-radius: 2px;
        transition: all 0.3s ease;
    }
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 1.5rem;
    left: 1.5rem;
    width: 50px;
    height: 50px;
    background: #2563eb;
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 1.125rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.25);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px 0 rgba(37, 99, 235, 0.35);
}

/* Smooth animations */
* {
    scroll-behavior: smooth;
}

/* Loading state */
body.loaded {
    opacity: 1;
}

/* Footer improvements */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #ffffff !important;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff !important;
    font-weight: 700;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-links a:hover {
    color: #60a5fa !important;
    padding-left: 0.5rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7) !important;
    margin: 0;
    font-size: 0.875rem;
}

.footer-bottom a {
    color: #60a5fa !important;
    text-decoration: none;
    font-weight: 600;
}

.footer-bottom a:hover {
    color: #93c5fd !important;
    text-decoration: underline;
}

/* Social links */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    background: #2563eb !important;
    color: #ffffff !important;
    transform: translateY(-2px);
}

/* Contact info in footer */
.contact-info-footer {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
}

.contact-item i {
    color: #60a5fa !important;
    width: 20px;
    font-size: 1rem;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* ===== Additional Contact Section Fixes ===== */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-top: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 1rem !important;
    padding: 2rem !important;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #3b82f6);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.contact-card.card-secondary::before {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.contact-card.card-accent::before {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
    border-color: #2563eb !important;
}

.contact-card:hover::before {
    transform: scaleX(1);
}

.contact-card .icon {
    margin-bottom: 1rem;
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
}

.contact-card h4 {
    color: #111827 !important;
    font-weight: 700 !important;
    margin-bottom: 0.75rem;
    font-size: 1.125rem;
}

.contact-card p {
    color: #4b5563 !important;
    font-weight: 500 !important;
    margin-bottom: 1.25rem;
    font-size: 1rem;
}

/* ===== Additional Pricing Section Fixes ===== */
.pricing-card {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    color: #1f2937 !important;
    border-radius: 1.5rem !important;
    padding: 2.5rem !important;
    position: relative;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #34d399);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.pricing-card.featured {
    border-color: #2563eb !important;
    background: #ffffff !important;
}

.pricing-card.featured::before {
    background: linear-gradient(90deg, #2563eb, #3b82f6);
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.pricing-card:hover::before {
    transform: scaleX(1);
}

/* Mobile contact and pricing fixes */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-card {
        padding: 1.5rem !important;
    }

    .pricing-card {
        padding: 2rem !important;
    }
}
