// ===== Language Management System =====

class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.translations = {};
        this.init();
    }
    
    init() {
        this.loadTranslations();
        this.setupLanguageToggle();
        this.loadSavedLanguage();
        this.updatePageDirection();
    }
    
    loadTranslations() {
        this.translations = {
            ar: {
                // Navigation
                'الرئيسية': 'الرئيسية',
                'الخدمات': 'الخدمات',
                'الأسعار': 'الأسعار',
                'من نحن': 'من نحن',
                'اتصل بنا': 'اتصل بنا',
                'تسجيل الدخول': 'تسجيل الدخول',
                'إنشاء حساب': 'إنشاء حساب',
                
                // Services
                'خدمات الاستضافة': 'خدمات الاستضافة',
                'التسويق الإلكتروني': 'التسويق الإلكتروني',
                'تصميم المواقع': 'تصميم المواقع',
                'إدارة السيرفرات': 'إدارة السيرفرات',
                
                // Hero Section
                'نقرة هوست': 'نقرة هوست',
                'حلولك الرقمية المتكاملة': 'حلولك الرقمية المتكاملة',
                'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة': 'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة',
                'استكشف خدماتنا': 'استكشف خدماتنا',
                'تواصل معنا': 'تواصل معنا',
                
                // Features
                'لماذا نقرة هوست؟': 'لماذا نقرة هوست؟',
                'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني': 'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني',
                'أداء فائق السرعة': 'أداء فائق السرعة',
                'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية': 'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية',
                'حماية متقدمة': 'حماية متقدمة',
                'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية': 'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية',
                'دعم فني متميز': 'دعم فني متميز',
                'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية': 'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية',
                
                // Services Section
                'خدماتنا المتميزة': 'خدماتنا المتميزة',
                'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك': 'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك',
                'خدمات إضافية': 'خدمات إضافية',
                
                // Floating Cards
                'استضافة موثوقة': 'استضافة موثوقة',
                'تسويق فعال': 'تسويق فعال',
                'دعم 24/7': 'دعم 24/7'
            },
            en: {
                // Navigation
                'الرئيسية': 'Home',
                'الخدمات': 'Services',
                'الأسعار': 'Pricing',
                'من نحن': 'About',
                'اتصل بنا': 'Contact',
                'تسجيل الدخول': 'Login',
                'إنشاء حساب': 'Register',
                
                // Services
                'خدمات الاستضافة': 'Hosting Services',
                'التسويق الإلكتروني': 'Digital Marketing',
                'تصميم المواقع': 'Web Design',
                'إدارة السيرفرات': 'Server Management',
                
                // Hero Section
                'نقرة هوست': 'Nakra Host',
                'حلولك الرقمية المتكاملة': 'Your Complete Digital Solutions',
                'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة': 'We provide the best web hosting and digital marketing services with 24/7 premium technical support',
                'استكشف خدماتنا': 'Explore Our Services',
                'تواصل معنا': 'Contact Us',
                
                // Features
                'لماذا نقرة هوست؟': 'Why Nakra Host?',
                'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني': 'We provide integrated solutions combining hosting and digital marketing',
                'أداء فائق السرعة': 'Lightning Fast Performance',
                'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية': 'High-performance SSD servers with global CDN for exceptional loading speeds',
                'حماية متقدمة': 'Advanced Security',
                'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية': 'DDoS protection and advanced firewall with automatic daily backups',
                'دعم فني متميز': 'Premium Support',
                'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية': 'Professional technical support team available 24/7 in Arabic and English',
                
                // Services Section
                'خدماتنا المتميزة': 'Our Premium Services',
                'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك': 'We offer a comprehensive range of digital services for your business growth',
                'خدمات إضافية': 'Additional Services',
                
                // Floating Cards
                'استضافة موثوقة': 'Reliable Hosting',
                'تسويق فعال': 'Effective Marketing',
                'دعم 24/7': '24/7 Support'
            }
        };
    }
    
    setupLanguageToggle() {
        const langToggle = document.getElementById('langToggle');
        if (langToggle) {
            langToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleLanguage();
            });
        }
    }

    toggleLanguage() {
        const langToggle = document.getElementById('langToggle');

        // Add switching animation
        if (langToggle) {
            langToggle.classList.add('switching');

            // Remove animation class after animation completes
            setTimeout(() => {
                langToggle.classList.remove('switching');
            }, 600);
        }

        // Toggle language with slight delay for better UX
        setTimeout(() => {
            this.currentLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
            this.updateLanguage();
            this.saveLanguagePreference();
            this.updateLanguageButton();
        }, 200);
    }

    updateLanguageButton() {
        const langCurrent = document.querySelector('.lang-current');
        const langSwitch = document.querySelector('.lang-switch');

        if (langCurrent && langSwitch) {
            if (this.currentLanguage === 'ar') {
                langCurrent.textContent = 'عربي';
                langSwitch.textContent = 'EN';
            } else {
                langCurrent.textContent = 'EN';
                langSwitch.textContent = 'عربي';
            }
        }
    }
    
    updateLanguage() {
        // Update HTML attributes
        const html = document.documentElement;
        const body = document.body;
        
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.style.fontFamily = 'var(--font-arabic)';
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            body.style.fontFamily = 'var(--font-english)';
        }
        
        // Update language toggle button
        this.updateLanguageToggle();
        
        // Update all translatable elements
        this.updateTranslatableElements();
        
        // Update page direction styles
        this.updatePageDirection();
        
        // Trigger custom event
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }
    
    updateLanguageToggle() {
        const langToggle = document.getElementById('langToggle');
        const langText = langToggle?.querySelector('.lang-text');
        
        if (langText) {
            langText.textContent = this.currentLanguage === 'ar' ? 'English' : 'العربية';
        }
    }
    
    updateTranslatableElements() {
        // Update elements with data-ar and data-en attributes
        const translatableElements = document.querySelectorAll('[data-ar][data-en]');
        
        translatableElements.forEach(element => {
            const arabicText = element.getAttribute('data-ar');
            const englishText = element.getAttribute('data-en');
            
            if (this.currentLanguage === 'ar') {
                element.textContent = arabicText;
            } else {
                element.textContent = englishText;
            }
        });
        
        // Update elements with translation keys
        const elementsWithKeys = document.querySelectorAll('[data-translate]');
        
        elementsWithKeys.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }
    
    updatePageDirection() {
        const body = document.body;
        const isRTL = this.currentLanguage === 'ar';
        
        // Update CSS custom properties for direction
        document.documentElement.style.setProperty('--text-align', isRTL ? 'right' : 'left');
        document.documentElement.style.setProperty('--text-align-opposite', isRTL ? 'left' : 'right');
        document.documentElement.style.setProperty('--margin-start', isRTL ? 'margin-right' : 'margin-left');
        document.documentElement.style.setProperty('--margin-end', isRTL ? 'margin-left' : 'margin-right');
        document.documentElement.style.setProperty('--padding-start', isRTL ? 'padding-right' : 'padding-left');
        document.documentElement.style.setProperty('--padding-end', isRTL ? 'padding-left' : 'padding-right');
        
        // Update navigation arrows
        const arrows = document.querySelectorAll('.fa-arrow-left, .fa-arrow-right');
        arrows.forEach(arrow => {
            if (isRTL) {
                arrow.classList.remove('fa-arrow-right');
                arrow.classList.add('fa-arrow-left');
            } else {
                arrow.classList.remove('fa-arrow-left');
                arrow.classList.add('fa-arrow-right');
            }
        });
        
        // Update floating cards positions for RTL
        const floatingCards = document.querySelectorAll('.floating-card');
        floatingCards.forEach(card => {
            if (isRTL) {
                card.style.transform = 'scaleX(-1)';
            } else {
                card.style.transform = 'scaleX(1)';
            }
        });
    }
    
    getTranslation(key) {
        return this.translations[this.currentLanguage]?.[key] || key;
    }
    
    saveLanguagePreference() {
        localStorage.setItem('nakra-host-language', this.currentLanguage);
    }
    
    loadSavedLanguage() {
        const savedLanguage = localStorage.getItem('nakra-host-language');
        if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
            this.currentLanguage = savedLanguage;
            this.updateLanguage();
        }
    }
    
    // Public methods for external use
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    setLanguage(language) {
        if (language === 'ar' || language === 'en') {
            this.currentLanguage = language;
            this.updateLanguage();
            this.saveLanguagePreference();
        }
    }
    
    translate(key) {
        return this.getTranslation(key);
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.languageManager = new LanguageManager();
});

// Utility function for dynamic content translation
function translateText(text, targetLanguage = null) {
    const lang = targetLanguage || window.languageManager?.getCurrentLanguage() || 'ar';
    
    // Simple translation mapping for common terms
    const quickTranslations = {
        ar: {
            'Loading...': 'جاري التحميل...',
            'Error': 'خطأ',
            'Success': 'نجح',
            'Submit': 'إرسال',
            'Cancel': 'إلغاء',
            'Close': 'إغلاق',
            'Save': 'حفظ',
            'Delete': 'حذف',
            'Edit': 'تعديل',
            'Add': 'إضافة',
            'Search': 'بحث',
            'Filter': 'تصفية',
            'Sort': 'ترتيب',
            'Next': 'التالي',
            'Previous': 'السابق',
            'Page': 'صفحة',
            'of': 'من',
            'Show': 'عرض',
            'Hide': 'إخفاء',
            'More': 'المزيد',
            'Less': 'أقل'
        },
        en: {
            'جاري التحميل...': 'Loading...',
            'خطأ': 'Error',
            'نجح': 'Success',
            'إرسال': 'Submit',
            'إلغاء': 'Cancel',
            'إغلاق': 'Close',
            'حفظ': 'Save',
            'حذف': 'Delete',
            'تعديل': 'Edit',
            'إضافة': 'Add',
            'بحث': 'Search',
            'تصفية': 'Filter',
            'ترتيب': 'Sort',
            'التالي': 'Next',
            'السابق': 'Previous',
            'صفحة': 'Page',
            'من': 'of',
            'عرض': 'Show',
            'إخفاء': 'Hide',
            'المزيد': 'More',
            'أقل': 'Less'
        }
    };
    
    return quickTranslations[lang]?.[text] || text;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LanguageManager, translateText };
}
