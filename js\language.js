// ===== Language Management System =====

class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.translations = {};
        this.init();
    }
    
    init() {
        this.loadTranslations();
        this.setupLanguageToggle();
        this.loadSavedLanguage();
        this.updatePageDirection();
        // Ensure translations are applied on page load
        setTimeout(() => {
            this.updateTranslatableElements();
            this.updatePricingFeatures();
        }, 100);
        // Additional update after DOM is fully loaded
        setTimeout(() => {
            this.updateTranslatableElements();
            this.updatePricingFeatures();
        }, 500);
    }
    
    loadTranslations() {
        this.translations = {
            ar: {
                // Navigation
                'الرئيسية': 'الرئيسية',
                'الخدمات': 'الخدمات',
                'الأسعار': 'الأسعار',
                'من نحن': 'من نحن',
                'اتصل بنا': 'اتصل بنا',
                'تسجيل الدخول': 'تسجيل الدخول',
                'إنشاء حساب': 'إنشاء حساب',
                
                // Services
                'خدمات الاستضافة': 'خدمات الاستضافة',
                'التسويق الإلكتروني': 'التسويق الإلكتروني',
                'تصميم المواقع': 'تصميم المواقع',
                'إدارة السيرفرات': 'إدارة السيرفرات',
                
                // Hero Section
                'نقرة هوست': 'نقرة هوست',
                'حلولك الرقمية المتكاملة': 'حلولك الرقمية المتكاملة',
                'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة': 'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة',
                'استكشف خدماتنا': 'استكشف خدماتنا',
                'تواصل معنا': 'تواصل معنا',
                
                // Features
                'لماذا نقرة هوست؟': 'لماذا نقرة هوست؟',
                'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني': 'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني',
                'أداء فائق السرعة': 'أداء فائق السرعة',
                'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية': 'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية',
                'حماية متقدمة': 'حماية متقدمة',
                'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية': 'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية',
                'دعم فني متميز': 'دعم فني متميز',
                'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية': 'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية',
                
                // Services Section
                'خدماتنا المتميزة': 'خدماتنا المتميزة',
                'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك': 'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك',
                'خدمات إضافية': 'خدمات إضافية',
                
                // Floating Cards
                'استضافة موثوقة': 'استضافة موثوقة',
                'تسويق فعال': 'تسويق فعال',
                'دعم 24/7': 'دعم 24/7'
            },
            en: {
                // Navigation
                'الرئيسية': 'Home',
                'الخدمات': 'Services',
                'الأسعار': 'Pricing',
                'من نحن': 'About',
                'اتصل بنا': 'Contact',
                'تسجيل الدخول': 'Login',
                'إنشاء حساب': 'Register',
                
                // Services
                'خدمات الاستضافة': 'Hosting Services',
                'التسويق الإلكتروني': 'Digital Marketing',
                'تصميم المواقع': 'Web Design',
                'إدارة السيرفرات': 'Server Management',
                
                // Hero Section
                'نقرة هوست': 'Nakra Host',
                'حلولك الرقمية المتكاملة': 'Your Complete Digital Solutions',
                'نقدم أفضل خدمات استضافة المواقع والتسويق الإلكتروني مع دعم فني متميز على مدار الساعة': 'We provide the best web hosting and digital marketing services with 24/7 premium technical support',
                'استكشف خدماتنا': 'Explore Our Services',
                'تواصل معنا': 'Contact Us',
                
                // Features
                'لماذا نقرة هوست؟': 'Why Nakra Host?',
                'نحن نقدم حلول متكاملة تجمع بين الاستضافة والتسويق الإلكتروني': 'We provide integrated solutions combining hosting and digital marketing',
                'أداء فائق السرعة': 'Lightning Fast Performance',
                'سيرفرات SSD عالية الأداء مع شبكة CDN عالمية لضمان سرعة تحميل استثنائية': 'High-performance SSD servers with global CDN for exceptional loading speeds',
                'حماية متقدمة': 'Advanced Security',
                'حماية DDoS وجدار حماية متقدم مع نسخ احتياطية يومية تلقائية': 'DDoS protection and advanced firewall with automatic daily backups',
                'دعم فني متميز': 'Premium Support',
                'فريق دعم فني محترف متاح 24/7 باللغة العربية والإنجليزية': 'Professional technical support team available 24/7 in Arabic and English',
                
                // Services Section
                'خدماتنا المتميزة': 'Our Premium Services',
                'نقدم مجموعة شاملة من الخدمات الرقمية لنمو أعمالك': 'We offer a comprehensive range of digital services for your business growth',
                'خدمات إضافية': 'Additional Services',
                
                // Floating Cards
                'استضافة موثوقة': 'Reliable Hosting',
                'تسويق فعال': 'Effective Marketing',
                'دعم 24/7': '24/7 Support',

                // Additional translations
                'الأفضل في الشرق الأوسط': 'Best in Middle East',
                'عميل راضي': 'Happy Clients',
                'وقت التشغيل': 'Uptime',
                'دعم فني': 'Support',
                'ابدأ الآن': 'Get Started',
                'عرض الأسعار': 'View Pricing',
                'ساعة دعم': 'Hours Support',
                'خدماتنا': 'Our Services',
                'الاستضافة المشتركة': 'Shared Hosting',
                'استضافة الريسيلر': 'Reseller Hosting',
                'روابط مهمة': 'Important Links',
                'من نحن': 'About Us',
                'الأسعار': 'Pricing',
                'الموقع الرئيسي': 'Main Site',
                'اتصل بنا الآن': 'Call us now',
                'تواصل معنا عبر الواتساب': 'Contact us via WhatsApp',
                'إدارة وسائل التواصل': 'Social Media Management',
                'تصميم المحتوى': 'Content Design',
                'جدولة المنشورات': 'Post Scheduling',
                'تحليل الأداء': 'Performance Analytics',
                'استشارة مجانية': 'Free Consultation',
                'ابتداءً من': 'Starting from',
                '/شهرياً': '/month',
                'واتساب': 'WhatsApp',
                'تواصل عبر واتساب': 'Chat on WhatsApp',
                'الأكثر طلباً': 'Most Popular',
                'الأكثر شعبية': 'Most Popular',

                // Additional missing translations
                'نشط الآن': 'Live Now',
                'لوحة التحكم': 'Control Panel',
                'استخدام الخادم': 'Server Usage',
                'استخدام الذاكرة': 'Memory Usage',
                'خدمات احترافية متكاملة': 'Complete Professional Services',
                'استضافة مشتركة بأسعار تنافسية': 'Shared Hosting at Competitive Prices',
                'استضافة ريسيلر مربحة': 'Profitable Reseller Hosting',
                'استضافة ووردبريس محسنة': 'Optimized WordPress Hosting',
                'خوادم VPS عالية الأداء': 'High Performance VPS Servers',
                'دعم فني 24/7': '24/7 Technical Support',
                'اطلب الآن': 'Order Now',
                'وقت التشغيل 99.9% - 500+ عميل راضي': '99.9% Uptime - 500+ Happy Clients',
                'إعلانات جوجل الاحترافية': 'Professional Google Ads',
                'فيسبوك وإنستجرام': 'Facebook & Instagram',
                'تيك توك وسناب شات': 'TikTok & Snapchat',
                'تحليل وتقارير مفصلة': 'Analytics & Detailed Reports',
                'استهداف دقيق للعملاء': 'Precise Customer Targeting',
                'زيادة المبيعات 300% - 50+ حملة ناجحة': '300% Sales Increase - 50+ Successful Campaigns',
                'برمجة وتطوير المواقع': 'Web Development & Programming',
                'تصميم متجاوب احترافي': 'Professional Responsive Design',
                'متاجر إلكترونية متكاملة': 'Complete E-commerce Stores',
                'مواقع ووردبريس مخصصة': 'Custom WordPress Websites',
                'تحسين SEO متقدم': 'Advanced SEO Optimization',
                'أمان وحماية عالية': 'High Security & Protection',
                'طلب عرض سعر': 'Request Quote',
                '100+ موقع مطور - 98% رضا العملاء': '100+ Websites Developed - 98% Client Satisfaction',

                // More translations
                'خطط استضافة بأسعار تنافسية': 'Competitive Hosting Plans',
                'خدمات إضافية': 'Additional Services',
                'إعلانات جوجل': 'Google Ads',
                'تحليل الكلمات المفتاحية': 'Keyword Analysis',
                'تقارير مفصلة': 'Detailed Reports',
                'إعداد الحملات': 'Campaign Setup',
                'تصميم موقع': 'Website Design',
                '/مشروع': '/project',
                'تصميم متجاوب': 'Responsive Design',
                'تحسين SEO': 'SEO Optimization',
                'أمان عالي': 'High Security',
                'نحن هنا لمساعدتك': 'We\'re Here to Help',
                'اتصل بنا': 'Call Us',
                'اتصل الآن': 'Call Now',
                'راسلنا': 'Email Us',
                'أرسل إيميل': 'Send Email',
                'الموقع الرئيسي': 'Main Website',
                'زيارة الموقع': 'Visit Website',
                'الاسم الكامل': 'Full Name',
                'البريد الإلكتروني': 'Email Address',
                'رقم الهاتف': 'Phone Number',
                'الموضوع': 'Subject',
                'الرسالة': 'Message',
                'تواصل معنا': 'Contact Info',
                'مصر - نبروة سيتي': 'Egypt - Nabarwa City',
                'جميع الحقوق محفوظة': 'All rights reserved',
                'تم إنشاء الموقع بواسطة فريق': 'Website created by',
                'أرسل لنا رسالة': 'Send us a Message',
                'إرسال الرسالة': 'Send Message',
                'نقرة للتسويق الإلكتروني': 'Nakra Digital Marketing'
            }
        };
    }
    
    setupLanguageToggle() {
        const langToggle = document.getElementById('langToggle');
        if (langToggle) {
            langToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleLanguage();

                // Force immediate update of pricing features
                setTimeout(() => {
                    this.updatePricingFeatures();
                }, 50);

                setTimeout(() => {
                    this.updatePricingFeatures();
                }, 200);
            });
        }
    }

    toggleLanguage() {
        const langToggle = document.getElementById('langToggle');

        // Add switching animation
        if (langToggle) {
            langToggle.classList.add('switching');

            // Remove animation class after animation completes
            setTimeout(() => {
                langToggle.classList.remove('switching');
            }, 600);
        }

        // Toggle language with slight delay for better UX
        setTimeout(() => {
            this.currentLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
            this.updateLanguage();
            this.saveLanguagePreference();
            this.updateLanguageButton();
        }, 200);
    }

    updateLanguageButton() {
        const langCurrent = document.querySelector('.lang-current');
        const langSwitch = document.querySelector('.lang-switch');

        if (langCurrent && langSwitch) {
            if (this.currentLanguage === 'ar') {
                langCurrent.textContent = 'عربي';
                langSwitch.textContent = 'EN';
            } else {
                langCurrent.textContent = 'EN';
                langSwitch.textContent = 'عربي';
            }
        }
    }
    
    updateLanguage() {
        // Update HTML attributes
        const html = document.documentElement;
        const body = document.body;
        
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.style.fontFamily = 'var(--font-arabic)';
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            body.style.fontFamily = 'var(--font-english)';
        }
        
        // Update language toggle button
        this.updateLanguageToggle();
        
        // Update all translatable elements
        this.updateTranslatableElements();
        
        // Update page direction styles
        this.updatePageDirection();
        
        // Trigger custom event
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));

        // Force update all elements after a short delay to catch any dynamically loaded content
        setTimeout(() => {
            this.updateTranslatableElements();
            this.updatePricingFeatures();
        }, 200);

        // Additional update for pricing features
        setTimeout(() => {
            this.updatePricingFeatures();
        }, 400);

        // Force multiple updates to ensure pricing features are translated
        setTimeout(() => {
            this.updatePricingFeatures();
        }, 600);

        setTimeout(() => {
            this.updatePricingFeatures();
        }, 1000);

        // Final update to ensure everything is translated
        setTimeout(() => {
            this.updatePricingFeatures();
        }, 1500);
    }
    
    updateLanguageToggle() {
        const langToggle = document.getElementById('langToggle');
        const langText = langToggle?.querySelector('.lang-text');
        
        if (langText) {
            langText.textContent = this.currentLanguage === 'ar' ? 'English' : 'العربية';
        }
    }
    
    updateTranslatableElements() {
        // Update elements with data-ar and data-en attributes
        const translatableElements = document.querySelectorAll('[data-ar][data-en]');

        translatableElements.forEach(element => {
            const arabicText = element.getAttribute('data-ar');
            const englishText = element.getAttribute('data-en');

            if (this.currentLanguage === 'ar') {
                element.textContent = arabicText;
            } else {
                element.textContent = englishText;
            }
        });

        // Update placeholders for form inputs
        const placeholderElements = document.querySelectorAll('[data-ar-placeholder][data-en-placeholder]');

        placeholderElements.forEach(element => {
            const arabicPlaceholder = element.getAttribute('data-ar-placeholder');
            const englishPlaceholder = element.getAttribute('data-en-placeholder');

            if (this.currentLanguage === 'ar') {
                element.placeholder = arabicPlaceholder;
            } else {
                element.placeholder = englishPlaceholder;
            }
        });

        // Update pricing plan features
        this.updatePricingFeatures();

        // Update elements with translation keys
        const elementsWithKeys = document.querySelectorAll('[data-translate]');

        elementsWithKeys.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }

    updatePricingFeatures() {
        // Define pricing features in both languages
        const pricingFeatures = {
            'ar': {
                'basic': [
                    '5 جيجا مساحة SSD',
                    '50 جيجا نقل بيانات',
                    '2 قاعدة بيانات MySQL',
                    'لوحة تحكم cPanel',
                    '2 موقع إلكتروني',
                    'دعم مباشر',
                    'دعم هاتفي'
                ],
                'premium': [
                    '15 جيجا مساحة SSD',
                    '100 جيجا نقل بيانات',
                    '10 قواعد بيانات MySQL',
                    'لوحة تحكم cPanel',
                    '10 مواقع إلكترونية',
                    'دعم مباشر',
                    'دعم هاتفي'
                ],
                'pro': [
                    '100 جيجا مساحة SSD',
                    'نقل بيانات غير محدود',
                    'قواعد بيانات غير محدودة',
                    'لوحة تحكم cPanel',
                    '100 موقع إلكتروني',
                    'دعم مباشر',
                    'دعم هاتفي'
                ]
            },
            'en': {
                'basic': [
                    '5GB SSD Storage',
                    '50GB Bandwidth',
                    '2 MySQL Databases',
                    'cPanel Control Panel',
                    '2 Websites',
                    'Live Support',
                    'Phone Support'
                ],
                'premium': [
                    '15GB SSD Storage',
                    '100GB Bandwidth',
                    '10 MySQL Databases',
                    'cPanel Control Panel',
                    '10 Websites',
                    'Live Support',
                    'Phone Support'
                ],
                'pro': [
                    '100GB SSD Storage',
                    'Unlimited Bandwidth',
                    'Unlimited Databases',
                    'cPanel Control Panel',
                    '100 Websites',
                    'Live Support',
                    'Phone Support'
                ]
            }
        };

        // Update each pricing card - try multiple selectors
        const pricingCards = document.querySelectorAll('.pricing-card');

        pricingCards.forEach((card, index) => {
            // Try to get plan type from data attribute first
            let planType = card.getAttribute('data-plan');

            // If no data-plan attribute, use index to determine plan type
            if (!planType) {
                const planTypes = ['basic', 'premium', 'pro'];
                planType = planTypes[index];
            }

            if (planType && pricingFeatures[this.currentLanguage] && pricingFeatures[this.currentLanguage][planType]) {
                const featuresContainer = card.querySelector('.pricing-features');
                if (featuresContainer) {
                    const featureItems = featuresContainer.querySelectorAll('.feature-item span');
                    const features = pricingFeatures[this.currentLanguage][planType];

                    featureItems.forEach((item, featureIndex) => {
                        if (features[featureIndex]) {
                            item.textContent = features[featureIndex];
                        }
                    });
                }
            }
        });
    }

    updatePageDirection() {
        const body = document.body;
        const isRTL = this.currentLanguage === 'ar';
        
        // Update CSS custom properties for direction
        document.documentElement.style.setProperty('--text-align', isRTL ? 'right' : 'left');
        document.documentElement.style.setProperty('--text-align-opposite', isRTL ? 'left' : 'right');
        document.documentElement.style.setProperty('--margin-start', isRTL ? 'margin-right' : 'margin-left');
        document.documentElement.style.setProperty('--margin-end', isRTL ? 'margin-left' : 'margin-right');
        document.documentElement.style.setProperty('--padding-start', isRTL ? 'padding-right' : 'padding-left');
        document.documentElement.style.setProperty('--padding-end', isRTL ? 'padding-left' : 'padding-right');
        
        // Update navigation arrows
        const arrows = document.querySelectorAll('.fa-arrow-left, .fa-arrow-right');
        arrows.forEach(arrow => {
            if (isRTL) {
                arrow.classList.remove('fa-arrow-right');
                arrow.classList.add('fa-arrow-left');
            } else {
                arrow.classList.remove('fa-arrow-left');
                arrow.classList.add('fa-arrow-right');
            }
        });
        
        // Update floating cards positions for RTL
        const floatingCards = document.querySelectorAll('.floating-card');
        floatingCards.forEach(card => {
            if (isRTL) {
                card.style.transform = 'scaleX(-1)';
            } else {
                card.style.transform = 'scaleX(1)';
            }
        });


    }
    
    getTranslation(key) {
        return this.translations[this.currentLanguage]?.[key] || key;
    }
    
    saveLanguagePreference() {
        localStorage.setItem('nakra-host-language', this.currentLanguage);
    }
    
    loadSavedLanguage() {
        const savedLanguage = localStorage.getItem('nakra-host-language');
        if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
            this.currentLanguage = savedLanguage;
            this.updateLanguage();
        }
    }
    
    // Public methods for external use
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    setLanguage(language) {
        if (language === 'ar' || language === 'en') {
            this.currentLanguage = language;
            this.updateLanguage();
            this.saveLanguagePreference();
        }
    }
    
    translate(key) {
        return this.getTranslation(key);
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.languageManager = new LanguageManager();

    // Additional initialization for dynamic content
    setTimeout(() => {
        if (window.languageManager) {
            window.languageManager.updateTranslatableElements();
            window.languageManager.updatePricingFeatures();
        }
    }, 500);

    // Force update after all content is loaded
    setTimeout(() => {
        if (window.languageManager) {
            window.languageManager.updatePricingFeatures();
        }
    }, 1000);

    // Set up mutation observer to watch for DOM changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if pricing cards were added
                const addedPricingCards = Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === 1 && (
                        node.classList?.contains('pricing-card') ||
                        node.querySelector?.('.pricing-card')
                    )
                );

                if (addedPricingCards && window.languageManager) {
                    setTimeout(() => {
                        window.languageManager.updatePricingFeatures();
                    }, 100);
                }
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// Utility function for dynamic content translation
function translateText(text, targetLanguage = null) {
    const lang = targetLanguage || window.languageManager?.getCurrentLanguage() || 'ar';
    
    // Simple translation mapping for common terms
    const quickTranslations = {
        ar: {
            'Loading...': 'جاري التحميل...',
            'Error': 'خطأ',
            'Success': 'نجح',
            'Submit': 'إرسال',
            'Cancel': 'إلغاء',
            'Close': 'إغلاق',
            'Save': 'حفظ',
            'Delete': 'حذف',
            'Edit': 'تعديل',
            'Add': 'إضافة',
            'Search': 'بحث',
            'Filter': 'تصفية',
            'Sort': 'ترتيب',
            'Next': 'التالي',
            'Previous': 'السابق',
            'Page': 'صفحة',
            'of': 'من',
            'Show': 'عرض',
            'Hide': 'إخفاء',
            'More': 'المزيد',
            'Less': 'أقل'
        },
        en: {
            'جاري التحميل...': 'Loading...',
            'خطأ': 'Error',
            'نجح': 'Success',
            'إرسال': 'Submit',
            'إلغاء': 'Cancel',
            'إغلاق': 'Close',
            'حفظ': 'Save',
            'حذف': 'Delete',
            'تعديل': 'Edit',
            'إضافة': 'Add',
            'بحث': 'Search',
            'تصفية': 'Filter',
            'ترتيب': 'Sort',
            'التالي': 'Next',
            'السابق': 'Previous',
            'صفحة': 'Page',
            'من': 'of',
            'عرض': 'Show',
            'إخفاء': 'Hide',
            'المزيد': 'More',
            'أقل': 'Less'
        }
    };
    
    return quickTranslations[lang]?.[text] || text;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LanguageManager, translateText };
}
